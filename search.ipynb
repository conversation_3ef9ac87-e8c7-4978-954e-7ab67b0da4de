from typing import List
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_groq import ChatGroq
from dotenv import load_dotenv
from langchain_chroma import Chroma
from langchain.document_loaders import PyPDFLoader
import os


load_dotenv()
llm = ChatGroq( model="llama-3.1-8b-instant")
llm

loader = PyPDFLoader("document/budget_union.pdf")
documents = loader.load()

from langchain_huggingface import HuggingFaceEmbeddings
embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-mpnet-base-v2")

vector_store = Chroma.from_documents(
    documents=documents,
    embedding=embeddings,
    persist_directory="./chroma_langchain_db"
)

retriever = vector_store.as_retriever(search_kwargs={"k": 3})  # Get top 3 most relevant chunks

from langchain.chains import RetrievalQA

qa_chain = RetrievalQA.from_chain_type(
    llm=llm,
    retriever=retriever,
    chain_type="stuff"  # Other types: "map_reduce", "refine"
)


query = " what is the Nuclear Energy Mission for Viksit Bharat ?"
result = qa_chain.run(query)

print(result)

query = "can you tell me about the revised tax structure ?"
result = qa_chain.run(query)

print(result)


# Define the state structure
class GraphState(TypedDict):
    question: str
    documents: List  # could be Document objects
    generation: str

# Node functions
def retrieve(state: GraphState):
    # Retrieves from vector store
    documents = retrieve_from_vector_store(state["question"])
    return {"question": state["question"], "documents": documents}

def grade_documents(state: GraphState):
    # Filters relevant docs
    filtered = [d for d in state["documents"] if is_relevant(d, state["question"])]
    return {"question": state["question"], "documents": filtered}

def transform_query(state: GraphState):
    # Rewrites the query
    better_q = rewriter(state["question"])
    return {"question": better_q, "documents": state["documents"]}

def web_search(state: GraphState):
    # Web search using Tavily
    search_tool = TavilySearchResults(max_results=3)
    docs = search_tool.invoke({"query": state["question"]})
    return {"question": state["question"], "documents": docs}

def generate(state: GraphState):
    # Generate answer using LLM + context
    answer = rag_chain.invoke({"context": state["documents"], "question": state["question"]})
    return {"question": state["question"], "documents": state["documents"], "generation": answer}

def grade_generation(state: GraphState):
    # Check for hallucination and question relevance
    if not is_grounded(state["generation"], state["documents"]):
        return "not_grounded"
    if not answers_question(state["generation"], state["question"]):
        return "not_useful"
    return "useful"

def decide_to_generate(state: GraphState):
    return "generate" if state["documents"] else "transform_query"

# Build the graph
workflow = StateGraph(GraphState)

workflow.add_node("retrieve", retrieve)
workflow.add_node("grade_documents", grade_documents)
workflow.add_node("transform_query", transform_query)
workflow.add_node("web_search", web_search)
workflow.add_node("generate", generate)

workflow.add_conditional_edges(
    START,
    lambda s: "retrieve",  # Or route based on router logic
    {"retrieve": "retrieve"}
)

workflow.add_edge("retrieve", "grade_documents")

workflow.add_conditional_edges(
    "grade_documents",
    decide_to_generate,
    {"generate": "generate", "transform_query": "transform_query"}
)

workflow.add_edge("transform_query", "web_search")

workflow.add_edge("web_search", "generate")

workflow.add_conditional_edges(
    "generate",
    grade_generation,
    {
        "not_grounded": "web_search",
        "not_useful": "transform_query",
        "useful": END,
    },
)

app = workflow.compile()
app

app.invoke({"question": "tell me top 5 latest news in the world wide"})

