from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages 
from langchain.schema.messages import HumanMessage, BaseMessage
from langchain_groq import ChatGroq
from dotenv import load_dotenv
from langchain_core.rate_limiters import InMemoryRateLimiter
from langchain_community.tools.tavily_search import TavilySearchResults
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import InMemorySaver
load_dotenv()
import os

search_tool = TavilySearchResults(max_results=2)
tools = [search_tool]

memory = InMemorySaver()

llm = ChatGroq( model="gemma2-9b-it")
agent_executor = create_react_agent(llm, tools, prompt="You are a helpful assistant, if you don't know answer try using tools for finding answer")

agent_executor.invoke({"messages": [HumanMessage(content="what is the weather in delhi")]})

class AgentState(TypedDict):
    messages: Annotated[list[BaseMessage], add_messages]

graph = StateGraph(AgentState)

def agent(state: AgentState) -> AgentState:
    response = agent_executor.invoke({"messages": state['messages']})
    return {"messages": response["messages"]}

graph.add_node("agent", agent)
graph.add_edge(START, "agent")
graph.add_edge("agent", END)

workflow = graph.compile()
workflow

response = workflow.invoke({
    "messages": [HumanMessage(content="tell me about machine learning")]
})

print(response["messages"])

answer = response["messages"][-1].content
print(answer)

print(workflow.invoke({
    "messages": [HumanMessage(content="what are the current latest (14th august 2025) news in the world(tell me top 5 news)")]
})["messages"][-1].content)

