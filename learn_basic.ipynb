from langgraph.graph import StateGraph,START, END
from typing import TypedDict

## define state
class BMI_state(TypedDict):
    weight : float
    height : float
    bmi : float

def calculate_bmi(state: BMI_state) -> BMI_state:
    state['bmi'] = state['weight'] / (state['height']*state['height'])
    return state

## define graph
graph = StateGraph(BMI_state)

## add node
graph.add_node("bmi",calculate_bmi)

## add edges
graph.add_edge(START, "bmi")
graph.add_edge("bmi", END)

## compile
workflow = graph.compile()

workflow

print(workflow.invoke({'weight':75, 'height':1.75}))