{"cells": [{"cell_type": "code", "execution_count": null, "id": "44a2d1fc", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph,START, END\n", "from typing import TypedDict"]}, {"cell_type": "code", "execution_count": 38, "id": "a5d542da", "metadata": {}, "outputs": [], "source": ["## define state\n", "class BMI_state(TypedDict):\n", "    weight : float\n", "    height : float\n", "    bmi : float"]}, {"cell_type": "code", "execution_count": 43, "id": "943be6fd", "metadata": {}, "outputs": [], "source": ["def calculate_bmi(state: BMI_state) -> BMI_state:\n", "    state['bmi'] = state['weight'] / (state['height']*state['height'])\n", "    return state"]}, {"cell_type": "code", "execution_count": 44, "id": "69a220f8", "metadata": {}, "outputs": [], "source": ["## define graph\n", "graph = StateGraph(BMI_state)\n", "\n", "## add node\n", "graph.add_node(\"bmi\",calculate_bmi)\n", "\n", "## add edges\n", "graph.add_edge(START, \"bmi\")\n", "graph.add_edge(\"bmi\", END)\n", "\n", "## compile\n", "workflow = graph.compile()"]}, {"cell_type": "code", "execution_count": 45, "id": "bb11d825", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000002D3D36F0550>"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow"]}, {"cell_type": "code", "execution_count": 46, "id": "9226fde6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'weight': 75, 'height': 1.75, 'bmi': 24.489795918367346}\n"]}], "source": ["print(workflow.invoke({'weight':75, 'height':1.75}))"]}], "metadata": {"kernelspec": {"display_name": "lang", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}