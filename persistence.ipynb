{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0bc6d762", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from typing import TypedDict, Annotated\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "from langchain_groq import ChatGroq\n", "import dotenv\n", "import os\n", "from pydantic import BaseModel, Field"]}, {"cell_type": "code", "execution_count": 2, "id": "954f29e5", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x0000022DEB76DA90>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x0000022DEB76E660>, model_name='gemma2-9b-it', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["groq_api_key = dotenv.get_key(\".env\", \"GROQ_API_KEY\")\n", "\n", "os.environ[\"GROQ_API_KEY\"] = groq_api_key\n", "llm = ChatGroq( model=\"gemma2-9b-it\")\n", "llm"]}, {"cell_type": "code", "execution_count": 3, "id": "fd1d1bf5", "metadata": {}, "outputs": [], "source": ["class jokestate(TypedDict):\n", "    topic: str\n", "    joke: str\n", "    explanation: str"]}, {"cell_type": "code", "execution_count": 4, "id": "62e0c709", "metadata": {}, "outputs": [], "source": ["def generate_joke(state:jokestate):\n", "    prompt = f\"generate a joke which is humorous and funny, try to be creative, also generate joke in hinglish (hindi+english) on the topic {state['topic']}\"\n", "    response = llm.invoke(prompt)\n", "    \n", "    return {'joke': response}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def generate_explanation(state:jokestate):\n", "    prompt = f\"explain the joke {state['joke']}\"\n", "    response = llm.invoke(prompt)\n", "    \n", "    return {'explanation': response}"]}, {"cell_type": "code", "execution_count": 6, "id": "0b62f93c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x0000022DEB76FCB0>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = StateGraph(jokestate)\n", "\n", "graph.add_node('joke', generate_joke)\n", "graph.add_node('explanation', generate_explanation)\n", "\n", "graph.add_edge(START, 'joke')\n", "graph.add_edge('joke', 'explanation')\n", "graph.add_edge('explanation', END)\n", "\n", "checkpointer = InMemorySaver()\n", "workflow = graph.compile(checkpointer=checkpointer)\n", "workflow"]}, {"cell_type": "code", "execution_count": 9, "id": "20884854", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'indian railways',\n", " 'joke': AIMess<PERSON>(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON><PERSON>, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211}),\n", " 'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\":{\"thread_id\":'1'}}\n", "workflow.invoke({'topic': 'indian railways'}, config=config)"]}, {"cell_type": "code", "execution_count": 13, "id": "17ef3451", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211}), 'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b91f-6c02-8002-3d480ee12361'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}}, created_at='2025-08-11T04:52:55.604915+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}, next=('explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}}, created_at='2025-08-11T04:52:54.893975+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='464a5c14-bee7-8af3-57df-c91c0a73b8d2', name='explanation', path=('__pregel_pull', 'explanation'), error=None, interrupts=(), state=None, result={'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways'}, next=('joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}}, created_at='2025-08-11T04:52:53.877293+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, tasks=(PregelTask(id='42c33571-5b43-df35-46e9-7f725502fba0', name='joke', path=('__pregel_pull', 'joke'), error=None, interrupts=(), state=None, result={'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"Madam, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"Madam, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, metadata={'source': 'input', 'step': -1, 'parents': {}}, created_at='2025-08-11T04:52:53.869939+00:00', parent_config=None, tasks=(PregelTask(id='7764c3ab-18fd-b0cd-9b45-203768a88213', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'indian railways'}),), interrupts=())]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config=config))"]}, {"cell_type": "code", "execution_count": 12, "id": "61f532e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["StateSnapshot(values={'topic': 'indian railways'}, next=('joke',), config={'configurable': {'thread_id': '1', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}}, created_at='2025-08-11T04:52:53.877293+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, tasks=(PregelTask(id='42c33571-5b43-df35-46e9-7f725502fba0', name='joke', path=('__pregel_pull', 'joke'), error=None, interrupts=(), state=None, result={'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"Madam, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"Madam, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}),), interrupts=())"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.get_state({'configurable':{\"thread_id\":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}})"]}, {"cell_type": "code", "execution_count": 14, "id": "beb94baa", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'indian railways',\n", " 'joke': AIMessage(content='##  Joke in Hinglish:\\n\\nWhy did the chai vendor bring a ladder to the railway platform? \\n\\n**Kyunki uske chai ki dukaan \"platform se upar\" thi!**  \\n\\n(Because his chai shop was \"above the platform\"!)\\n\\n##  Humorous and Creative Joke:\\n\\nWhy did the train go to the doctor?\\n\\n**Because it had a bad case of the wheeze-els!**  \\n\\n\\n\\nLet me know if you\\'d like to hear more jokes! \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 110, 'prompt_tokens': 39, 'total_tokens': 149, 'completion_time': 0.2, 'prompt_time': 0.001655111, 'queue_time': 0.246660269, 'total_time': 0.201655111}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--6ca73375-ec10-4985-b778-8ce1cd31bd6f-0', usage_metadata={'input_tokens': 39, 'output_tokens': 110, 'total_tokens': 149}),\n", " 'explanation': AIMessage(content='You\\'ve got two great jokes there! Let\\'s break them down:\\n\\n**Joke 1: The Chai Vendor**\\n\\n* **The Setup:** Why did the chai vendor bring a ladder to the railway platform?\\n* **The Punchline:** <PERSON><PERSON><PERSON> uske chai ki dukaan \"platform se upar\" thi! (Because his chai shop was \"above the platform\"!)\\n* **The Humor:** This joke plays on the literal meaning of the phrase \"platform se upar\" (above the platform).  Chai vendors usually set up shop *on* the platform, so the idea of a chai shop being *above* the platform is absurd and unexpected. The use of Hinglish adds to the playful, colloquial tone.\\n\\n**Joke 2: The Wheeze-els**\\n\\n* **The Setup:** Why did the train go to the doctor?\\n* **The Punchline:** Because it had a bad case of the wheeze-els!\\n* **The Humor:** This joke is a pun, using \"wheeze-els\" to sound like \"wheels.\"  The image of a train with a cold is funny because trains don\\'t get sick in the same way humans do.\\n\\n\\nI\\'d love to hear more jokes! 😄  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 263, 'prompt_tokens': 375, 'total_tokens': 638, 'completion_time': 0.478181818, 'prompt_time': 0.009215772, 'queue_time': 0.254191018, 'total_time': 0.48739759}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f3ae1fbc-8cff-45b3-85db-b3ff57c61846-0', usage_metadata={'input_tokens': 375, 'output_tokens': 263, 'total_tokens': 638})}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.invoke(None, {'configurable':{\"thread_id\":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}})"]}, {"cell_type": "code", "execution_count": 15, "id": "3a68ccb8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke in Hinglish:\\n\\nWhy did the chai vendor bring a ladder to the railway platform? \\n\\n**Kyunki uske chai ki dukaan \"platform se upar\" thi!**  \\n\\n(Because his chai shop was \"above the platform\"!)\\n\\n##  Humorous and Creative Joke:\\n\\nWhy did the train go to the doctor?\\n\\n**Because it had a bad case of the wheeze-els!**  \\n\\n\\n\\nLet me know if you\\'d like to hear more jokes! \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 110, 'prompt_tokens': 39, 'total_tokens': 149, 'completion_time': 0.2, 'prompt_time': 0.001655111, 'queue_time': 0.246660269, 'total_time': 0.201655111}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--6ca73375-ec10-4985-b778-8ce1cd31bd6f-0', usage_metadata={'input_tokens': 39, 'output_tokens': 110, 'total_tokens': 149}), 'explanation': AIMessage(content='You\\'ve got two great jokes there! Let\\'s break them down:\\n\\n**Joke 1: The Chai Vendor**\\n\\n* **The Setup:** Why did the chai vendor bring a ladder to the railway platform?\\n* **The Punchline:** Kyunki uske chai ki dukaan \"platform se upar\" thi! (Because his chai shop was \"above the platform\"!)\\n* **The Humor:** This joke plays on the literal meaning of the phrase \"platform se upar\" (above the platform).  Chai vendors usually set up shop *on* the platform, so the idea of a chai shop being *above* the platform is absurd and unexpected. The use of Hinglish adds to the playful, colloquial tone.\\n\\n**Joke 2: The Wheeze-els**\\n\\n* **The Setup:** Why did the train go to the doctor?\\n* **The Punchline:** Because it had a bad case of the wheeze-els!\\n* **The Humor:** This joke is a pun, using \"wheeze-els\" to sound like \"wheels.\"  The image of a train with a cold is funny because trains don\\'t get sick in the same way humans do.\\n\\n\\nI\\'d love to hear more jokes! 😄  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 263, 'prompt_tokens': 375, 'total_tokens': 638, 'completion_time': 0.478181818, 'prompt_time': 0.009215772, 'queue_time': 0.254191018, 'total_time': 0.48739759}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f3ae1fbc-8cff-45b3-85db-b3ff57c61846-0', usage_metadata={'input_tokens': 375, 'output_tokens': 263, 'total_tokens': 638})}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-53a9-624c-8002-64989589fcc3'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}}, created_at='2025-08-11T05:09:18.176908+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-4c48-6842-8001-7d4a7acf7f1c'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke in Hinglish:\\n\\nWhy did the chai vendor bring a ladder to the railway platform? \\n\\n**Kyunki uske chai ki dukaan \"platform se upar\" thi!**  \\n\\n(Because his chai shop was \"above the platform\"!)\\n\\n##  Humorous and Creative Joke:\\n\\nWhy did the train go to the doctor?\\n\\n**Because it had a bad case of the wheeze-els!**  \\n\\n\\n\\nLet me know if you\\'d like to hear more jokes! \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 110, 'prompt_tokens': 39, 'total_tokens': 149, 'completion_time': 0.2, 'prompt_time': 0.001655111, 'queue_time': 0.246660269, 'total_time': 0.201655111}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--6ca73375-ec10-4985-b778-8ce1cd31bd6f-0', usage_metadata={'input_tokens': 39, 'output_tokens': 110, 'total_tokens': 149})}, next=('explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-4c48-6842-8001-7d4a7acf7f1c'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}}, created_at='2025-08-11T05:09:17.403324+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='55ec3e10-0fad-7914-ab3e-72ece664930a', name='explanation', path=('__pregel_pull', 'explanation'), error=None, interrupts=(), state=None, result={'explanation': AIMessage(content='You\\'ve got two great jokes there! Let\\'s break them down:\\n\\n**Joke 1: The Chai Vendor**\\n\\n* **The Setup:** Why did the chai vendor bring a ladder to the railway platform?\\n* **The Punchline:** Kyunki uske chai ki dukaan \"platform se upar\" thi! (Because his chai shop was \"above the platform\"!)\\n* **The Humor:** This joke plays on the literal meaning of the phrase \"platform se upar\" (above the platform).  Chai vendors usually set up shop *on* the platform, so the idea of a chai shop being *above* the platform is absurd and unexpected. The use of Hinglish adds to the playful, colloquial tone.\\n\\n**Joke 2: The Wheeze-els**\\n\\n* **The Setup:** Why did the train go to the doctor?\\n* **The Punchline:** Because it had a bad case of the wheeze-els!\\n* **The Humor:** This joke is a pun, using \"wheeze-els\" to sound like \"wheels.\"  The image of a train with a cold is funny because trains don\\'t get sick in the same way humans do.\\n\\n\\nI\\'d love to hear more jokes! 😄  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 263, 'prompt_tokens': 375, 'total_tokens': 638, 'completion_time': 0.478181818, 'prompt_time': 0.009215772, 'queue_time': 0.254191018, 'total_time': 0.48739759}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f3ae1fbc-8cff-45b3-85db-b3ff57c61846-0', usage_metadata={'input_tokens': 375, 'output_tokens': 263, 'total_tokens': 638})}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211}), 'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b91f-6c02-8002-3d480ee12361'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}}, created_at='2025-08-11T04:52:55.604915+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}, next=('explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}}, created_at='2025-08-11T04:52:54.893975+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='464a5c14-bee7-8af3-57df-c91c0a73b8d2', name='explanation', path=('__pregel_pull', 'explanation'), error=None, interrupts=(), state=None, result={'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways'}, next=('joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}}, created_at='2025-08-11T04:52:53.877293+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, tasks=(PregelTask(id='42c33571-5b43-df35-46e9-7f725502fba0', name='joke', path=('__pregel_pull', 'joke'), error=None, interrupts=(), state=None, result={'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"Madam, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"Madam, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, metadata={'source': 'input', 'step': -1, 'parents': {}}, created_at='2025-08-11T04:52:53.869939+00:00', parent_config=None, tasks=(PregelTask(id='7764c3ab-18fd-b0cd-9b45-203768a88213', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'indian railways'}),), interrupts=())]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config=config))"]}, {"cell_type": "code", "execution_count": 17, "id": "178d4dbf", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'configurable': {'thread_id': '1',\n", "  'checkpoint_ns': '',\n", "  'checkpoint_id': '1f07671d-0d1d-635a-8001-e65530acb027'}}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.update_state({'configurable':{\"thread_id\":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386', 'checkpoint_ns':''}}, {'topic': 'samosa'})"]}, {"cell_type": "code", "execution_count": 18, "id": "193db99b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[StateSnapshot(values={'topic': 'samosa'}, next=('joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f07671d-0d1d-635a-8001-e65530acb027'}}, metadata={'source': 'update', 'step': 1, 'parents': {}}, created_at='2025-08-11T05:12:45.527923+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='82088cde-8f5b-35ce-8886-f3e8bff13b59', name='joke', path=('__pregel_pull', 'joke'), error=None, interrupts=(), state=None, result=None),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke in Hinglish:\\n\\nWhy did the chai vendor bring a ladder to the railway platform? \\n\\n**Kyunki uske chai ki dukaan \"platform se upar\" thi!**  \\n\\n(Because his chai shop was \"above the platform\"!)\\n\\n##  Humorous and Creative Joke:\\n\\nWhy did the train go to the doctor?\\n\\n**Because it had a bad case of the wheeze-els!**  \\n\\n\\n\\nLet me know if you\\'d like to hear more jokes! \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 110, 'prompt_tokens': 39, 'total_tokens': 149, 'completion_time': 0.2, 'prompt_time': 0.001655111, 'queue_time': 0.246660269, 'total_time': 0.201655111}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--6ca73375-ec10-4985-b778-8ce1cd31bd6f-0', usage_metadata={'input_tokens': 39, 'output_tokens': 110, 'total_tokens': 149}), 'explanation': AIMessage(content='You\\'ve got two great jokes there! Let\\'s break them down:\\n\\n**Joke 1: The Chai Vendor**\\n\\n* **The Setup:** Why did the chai vendor bring a ladder to the railway platform?\\n* **The Punchline:** Kyunki uske chai ki dukaan \"platform se upar\" thi! (Because his chai shop was \"above the platform\"!)\\n* **The Humor:** This joke plays on the literal meaning of the phrase \"platform se upar\" (above the platform).  Chai vendors usually set up shop *on* the platform, so the idea of a chai shop being *above* the platform is absurd and unexpected. The use of Hinglish adds to the playful, colloquial tone.\\n\\n**Joke 2: The Wheeze-els**\\n\\n* **The Setup:** Why did the train go to the doctor?\\n* **The Punchline:** Because it had a bad case of the wheeze-els!\\n* **The Humor:** This joke is a pun, using \"wheeze-els\" to sound like \"wheels.\"  The image of a train with a cold is funny because trains don\\'t get sick in the same way humans do.\\n\\n\\nI\\'d love to hear more jokes! 😄  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 263, 'prompt_tokens': 375, 'total_tokens': 638, 'completion_time': 0.478181818, 'prompt_time': 0.009215772, 'queue_time': 0.254191018, 'total_time': 0.48739759}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f3ae1fbc-8cff-45b3-85db-b3ff57c61846-0', usage_metadata={'input_tokens': 375, 'output_tokens': 263, 'total_tokens': 638})}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-53a9-624c-8002-64989589fcc3'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}}, created_at='2025-08-11T05:09:18.176908+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-4c48-6842-8001-7d4a7acf7f1c'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke in Hinglish:\\n\\nWhy did the chai vendor bring a ladder to the railway platform? \\n\\n**Kyunki uske chai ki dukaan \"platform se upar\" thi!**  \\n\\n(Because his chai shop was \"above the platform\"!)\\n\\n##  Humorous and Creative Joke:\\n\\nWhy did the train go to the doctor?\\n\\n**Because it had a bad case of the wheeze-els!**  \\n\\n\\n\\nLet me know if you\\'d like to hear more jokes! \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 110, 'prompt_tokens': 39, 'total_tokens': 149, 'completion_time': 0.2, 'prompt_time': 0.001655111, 'queue_time': 0.246660269, 'total_time': 0.201655111}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--6ca73375-ec10-4985-b778-8ce1cd31bd6f-0', usage_metadata={'input_tokens': 39, 'output_tokens': 110, 'total_tokens': 149})}, next=('explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f076715-4c48-6842-8001-7d4a7acf7f1c'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}}, created_at='2025-08-11T05:09:17.403324+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='55ec3e10-0fad-7914-ab3e-72ece664930a', name='explanation', path=('__pregel_pull', 'explanation'), error=None, interrupts=(), state=None, result={'explanation': AIMessage(content='You\\'ve got two great jokes there! Let\\'s break them down:\\n\\n**Joke 1: The Chai Vendor**\\n\\n* **The Setup:** Why did the chai vendor bring a ladder to the railway platform?\\n* **The Punchline:** Kyunki uske chai ki dukaan \"platform se upar\" thi! (Because his chai shop was \"above the platform\"!)\\n* **The Humor:** This joke plays on the literal meaning of the phrase \"platform se upar\" (above the platform).  Chai vendors usually set up shop *on* the platform, so the idea of a chai shop being *above* the platform is absurd and unexpected. The use of Hinglish adds to the playful, colloquial tone.\\n\\n**Joke 2: The Wheeze-els**\\n\\n* **The Setup:** Why did the train go to the doctor?\\n* **The Punchline:** Because it had a bad case of the wheeze-els!\\n* **The Humor:** This joke is a pun, using \"wheeze-els\" to sound like \"wheels.\"  The image of a train with a cold is funny because trains don\\'t get sick in the same way humans do.\\n\\n\\nI\\'d love to hear more jokes! 😄  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 263, 'prompt_tokens': 375, 'total_tokens': 638, 'completion_time': 0.478181818, 'prompt_time': 0.009215772, 'queue_time': 0.254191018, 'total_time': 0.48739759}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f3ae1fbc-8cff-45b3-85db-b3ff57c61846-0', usage_metadata={'input_tokens': 375, 'output_tokens': 263, 'total_tokens': 638})}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211}), 'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b91f-6c02-8002-3d480ee12361'}}, metadata={'source': 'loop', 'step': 2, 'parents': {}}, created_at='2025-08-11T04:52:55.604915+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, tasks=(), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways', 'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"<PERSON>am, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"<PERSON><PERSON>, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}, next=('explanation',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-b258-610d-8001-38547d402bbc'}}, metadata={'source': 'loop', 'step': 1, 'parents': {}}, created_at='2025-08-11T04:52:54.893975+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, tasks=(PregelTask(id='464a5c14-bee7-8af3-57df-c91c0a73b8d2', name='explanation', path=('__pregel_pull', 'explanation'), error=None, interrupts=(), state=None, result={'explanation': AIMessage(content='Here\\'s a breakdown of the jokes:\\n\\n**Joke 1 (English):**\\n\\n* **Setup:** Why did the train go to the doctor?\\n* **Punchline:** Because it had a **track** record of being late!\\n* **Explanation:** This is a pun. \"Track record\" has two meanings:\\n    *  A history of successes or failures.\\n    * The physical tracks that trains run on. \\n\\nThe joke plays on the double meaning to create humor.\\n\\n**Joke 2 (Hinglish):**\\n\\n* **Setup:** A man asks a woman on a train when it will arrive.\\n* **Punchline:** The woman replies that she doesn\\'t know today, but tomorrow it\\'s going to Delhi after reaching Mumbai!\\n* **Explanation:** This joke relies on absurdity. The woman\\'s response is illogical. Trains don\\'t typically travel from Mumbai to Delhi in a single journey and then back again the next day. The unexpected and nonsensical answer creates humor.\\n\\n\\nLet me know if you\\'d like to hear more jokes! 😊 \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 230, 'prompt_tokens': 464, 'total_tokens': 694, 'completion_time': 0.*********, 'prompt_time': 0.*********, 'queue_time': 0.*********, 'total_time': 0.*********}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--3f73f5a6-5731-4b31-9cca-56b302dfdead-0', usage_metadata={'input_tokens': 464, 'output_tokens': 230, 'total_tokens': 694})}),), interrupts=()),\n", " StateSnapshot(values={'topic': 'indian railways'}, next=('joke',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}}, metadata={'source': 'loop', 'step': 0, 'parents': {}}, created_at='2025-08-11T04:52:53.877293+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, tasks=(PregelTask(id='42c33571-5b43-df35-46e9-7f725502fba0', name='joke', path=('__pregel_pull', 'joke'), error=None, interrupts=(), state=None, result={'joke': AIMessage(content='##  Joke 1 (English)\\n\\nWhy did the train go to the doctor? \\n\\nBecause it had a **track** record of being late! \\n\\n\\n## Joke 2 (Hinglish)\\n\\n**Ek train ke andar ek aadmi aur ek aurat baith rahe the.**\\n\\n**Aadmi:** \"Madam, yeh train kab pahunchengi?\"\\n\\n**Aurat:** \"Aaj toh pata nahi, kal toh \\n\\nMumbai tak pahunchne ke baad bhi Delhi jana hai!\"\\n\\n\\n**Translation:**\\n\\nTwo people were sitting on a train.\\n\\n**Man:** \"Madam, when will this train arrive?\"\\n\\n**Woman:** \"I don\\'t know today, tomorrow it\\'s going to Delhi after reaching Mumbai!\"\\n\\n\\n\\nLet me know if you want to hear more! 😉  \\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 172, 'prompt_tokens': 39, 'total_tokens': 211, 'completion_time': 0.312727273, 'prompt_time': 0.001559099, 'queue_time': 0.247602082, 'total_time': 0.314286372}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--97637d5f-eb12-4ce2-8a80-ab043ed3bd4c-0', usage_metadata={'input_tokens': 39, 'output_tokens': 172, 'total_tokens': 211})}),), interrupts=()),\n", " StateSnapshot(values={}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f0766f0-a893-6f55-bfff-87d1d83b3bca'}}, metadata={'source': 'input', 'step': -1, 'parents': {}}, created_at='2025-08-11T04:52:53.869939+00:00', parent_config=None, tasks=(PregelTask(id='7764c3ab-18fd-b0cd-9b45-203768a88213', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'topic': 'indian railways'}),), interrupts=())]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["list(workflow.get_state_history(config=config))"]}, {"cell_type": "code", "execution_count": 19, "id": "de3fa763", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'topic': 'samosa',\n", " 'joke': AIMess<PERSON>(content=\"## Hindi-English Samosa Joke:\\n\\n**Why did the samosa cross the road?**\\n\\n**To prove to the chicken samosa it wasn't chicken!** \\n\\n**(खीचड़ी समोसा ने सड़क पार की वजह से चिकन समोसे को यह साबित करना था कि वह चिकन नहीं है!)**\\n\\n---\\n\\nLet me know if you'd like to hear another one! 😄 \\n\", additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 100, 'prompt_tokens': 39, 'total_tokens': 139, 'completion_time': 0.181818182, 'prompt_time': 0.002028221, 'queue_time': 0.24954856, 'total_time': 0.183846403}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--575cb6d2-bfc4-44ce-bbe3-eb9779685258-0', usage_metadata={'input_tokens': 39, 'output_tokens': 100, 'total_tokens': 139}),\n", " 'explanation': AIMessage(content='The joke plays on the word \"samosa\"  and its different interpretations. \\n\\n* **Literal meaning:** Samosa is a popular fried pastry in India, often filled with spiced potatoes or lentils.\\n* **Playful meaning:**  The joke introduces the idea of a \"chicken samosa,\" which doesn\\'t exist.\\n\\nThe punchline, \"To prove to the chicken samosa it wasn\\'t chicken!\", uses the absurdity of a chicken samosa to create humor. It implies that the samosa is trying to distinguish itself from something that it\\'s not, highlighting the nonsensical nature of the situation. \\n\\n\\nLet me know if you\\'d like to hear another joke! 😄\\n', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 146, 'prompt_tokens': 364, 'total_tokens': 510, 'completion_time': 0.265454545, 'prompt_time': 0.010166872, 'queue_time': 0.246209059, 'total_time': 0.275621417}, 'model_name': 'gemma2-9b-it', 'system_fingerprint': 'fp_10c08bf97d', 'service_tier': 'on_demand', 'finish_reason': 'stop', 'logprobs': None}, id='run--f442af55-3cdc-4132-94f7-16e123eeaf0c-0', usage_metadata={'input_tokens': 364, 'output_tokens': 146, 'total_tokens': 510})}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["workflow.invoke(None, {'configurable':{\"thread_id\":'1','checkpoint_id': '1f07671d-0d1d-635a-8001-e65530acb027'}})"]}], "metadata": {"kernelspec": {"display_name": "lang", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}