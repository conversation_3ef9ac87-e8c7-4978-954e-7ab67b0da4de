from typing import Annotated
from typing import TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import InMemorySaver
from langchain_groq import ChatGroq
import dotenv
import os
from pydantic import BaseModel, Field

groq_api_key = dotenv.get_key(".env", "GROQ_API_KEY")

os.environ["GROQ_API_KEY"] = groq_api_key
llm = ChatGroq( model="gemma2-9b-it")
llm

class jokestate(TypedDict):
    topic: str
    joke: str
    explanation: str

def generate_joke(state:jokestate):
    prompt = f"generate a joke which is humorous and funny, try to be creative, also generate joke in hinglish (hindi+english) on the topic {state['topic']}"
    response = llm.invoke(prompt)
    
    return {'joke': response}

def generate_explanation(state:jokestate):
    prompt = f"explain the joke {state['joke']}"
    response = llm.invoke(prompt)
    
    return {'explanation': response}

graph = StateGraph(jokestate)

graph.add_node('joke', generate_joke)
graph.add_node('explanation', generate_explanation)

graph.add_edge(START, 'joke')
graph.add_edge('joke', 'explanation')
graph.add_edge('explanation', END)

checkpointer = InMemorySaver()
workflow = graph.compile(checkpointer=checkpointer)
workflow

config = {"configurable":{"thread_id":'1'}}
workflow.invoke({'topic': 'indian railways'}, config=config)

list(workflow.get_state_history(config=config))

workflow.get_state({'configurable':{"thread_id":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}})

workflow.invoke(None, {'configurable':{"thread_id":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386'}})

list(workflow.get_state_history(config=config))

workflow.update_state({'configurable':{"thread_id":'1','checkpoint_id': '1f0766f0-a8a5-6ef3-8000-34107ee62386', 'checkpoint_ns':''}}, {'topic': 'samosa'})

list(workflow.get_state_history(config=config))

workflow.invoke(None, {'configurable':{"thread_id":'1','checkpoint_id': '1f07671d-0d1d-635a-8001-e65530acb027'}})