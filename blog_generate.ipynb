{"cells": [{"cell_type": "code", "execution_count": 14, "id": "87e3d15c", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages \n", "from langchain_groq import ChatGroq\n", "import dotenv"]}, {"cell_type": "code", "execution_count": 15, "id": "0cff1eb7", "metadata": {}, "outputs": [], "source": ["groq_api_key = dotenv.get_key(\".env\", \"GROQ_API_KEY\")"]}, {"cell_type": "code", "execution_count": 16, "id": "51677861", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x000001E4BE8F2210>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x000001E4BE8F1D10>, model_name='gemma2-9b-it', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "os.environ[\"GROQ_API_KEY\"] = groq_api_key\n", "llm = ChatGroq( model=\"gemma2-9b-it\")\n", "llm"]}, {"cell_type": "code", "execution_count": 17, "id": "acf32857", "metadata": {}, "outputs": [], "source": ["class blog_state(TypedDict):\n", "    title: str\n", "    outline: str\n", "    content: str\n", "    score: int"]}, {"cell_type": "code", "execution_count": 18, "id": "86350643", "metadata": {}, "outputs": [], "source": ["def create_outline(state: blog_state) -> blog_state:\n", "    title = state['title']\n", "    prompt = f\"Create an outline for a blog post titled '{title}'.\"\n", "    response = llm.invoke(prompt)\n", "    state['outline'] = response\n", "    return state"]}, {"cell_type": "code", "execution_count": 19, "id": "12b22195", "metadata": {}, "outputs": [], "source": ["def create_content(state: blog_state) -> blog_state:\n", "    outline = state['outline']\n", "    prompt = f\"Create a detailed blog post based on the outline: {outline}.\"\n", "    response = llm.invoke(prompt)\n", "    state['content'] = response\n", "    return state"]}, {"cell_type": "code", "execution_count": 20, "id": "92805a20", "metadata": {}, "outputs": [], "source": ["def evaluate_content(state: blog_state) -> blog_state:\n", "    outline = state['outline']\n", "    content = state['content']\n", "    prompt = f\"On the base of the outline: {outline} Evaluate the quality of the following blog post, give score from 0 to 10: content: {content}.\"\n", "    response = llm.invoke(prompt)\n", "    state['score'] = response\n", "    return state"]}, {"cell_type": "code", "execution_count": 21, "id": "1f113918", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x1e4be8f3250>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = StateGraph(blog_state)\n", "\n", "graph.add_node('outline', create_outline)\n", "graph.add_node('content', create_content)\n", "graph.add_node('evaluate', evaluate_content)"]}, {"cell_type": "code", "execution_count": 22, "id": "eec53a7b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000001E4BE8F3390>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.add_edge(START,'outline')\n", "graph.add_edge('outline', 'content')\n", "graph.add_edge('content', 'evaluate')\n", "graph.add_edge('evaluate', END)\n", "\n", "workflow = graph.compile()\n", "workflow"]}, {"cell_type": "code", "execution_count": 23, "id": "bdffb909", "metadata": {}, "outputs": [], "source": ["response = workflow.invoke({'title': 'The Future of AI in Everyday Life'})"]}, {"cell_type": "code", "execution_count": 24, "id": "2489de3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["## Blog Post Outline: The Future of AI in Everyday Life\n", "\n", "**I. Introduction**\n", "\n", "*  Hook: Start with a captivating example of AI already impacting daily life (e.g., personalized recommendations, voice assistants).\n", "*  Define AI in simple terms, avoiding jargon.\n", "*  Briefly state the purpose of the blog post: to explore how AI will continue to shape our everyday experiences.\n", "\n", "**II. Current Applications of AI**\n", "\n", "*  Provide concrete examples of AI already used in everyday life:\n", "    *  Smartphones (personalization, voice assistants)\n", "    *  Streaming services (recommendations)\n", "    *  Transportation (navigation apps, self-driving cars)\n", "    *  Healthcare (diagnosis assistance, drug discovery)\n", "*  Highlight the convenience, efficiency, and personalization AI brings to these areas.\n", "\n", "**III. Emerging Trends and Future Possibilities**\n", "\n", "*  **AI-powered Education:**\n", "    *  Personalized learning experiences\n", "    *  Automated grading and feedback\n", "    *  Virtual tutors and mentors\n", "*  **AI in Healthcare:**\n", "    *  Early disease detection and prevention\n", "    *  Precision medicine tailored to individual patients\n", "    *  Robot-assisted surgery\n", "*  **Smart Homes and Cities:**\n", "    *  Automated home management systems\n", "    *  Predictive maintenance for infrastructure\n", "    *  Enhanced public safety and security\n", "*  **AI and Creativity:**\n", "    *  AI-assisted writing, art, and music creation\n", "    *  New forms of interactive entertainment\n", "\n", "**IV. Potential Challenges and Ethical Considerations**\n", "\n", "*  **Job displacement:** Discuss the potential impact of AI on employment.\n", "*  **Data privacy and security:**  Explain the importance of protecting personal data used by AI systems.\n", "*  **Bias and fairness:** Address the risk of AI perpetuating existing societal biases.\n", "*  **Transparency and accountability:** Emphasize the need for explainable AI and clear responsibility for AI-driven decisions.\n", "\n", "**V. Conclusion**\n", "\n", "*  Reiterate the transformative potential of AI in everyday life.\n", "*  Encourage readers to engage in thoughtful discussions about the ethical implications of AI.\n", "*  End with a forward-looking statement about the exciting possibilities that lie ahead.\n", "\n", "\n", "\n", "\n"]}], "source": ["print(response['outline'].content)"]}, {"cell_type": "code", "execution_count": 25, "id": "c4ea398d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["##  The Future is Now: How AI is Shaping Our Everyday Lives \n", "\n", "Imagine a world where your phone anticipates your needs, your doctor diagnoses illnesses with unprecedented accuracy, and your city runs like a well-oiled machine. This isn't science fiction; it's the future powered by artificial intelligence (AI). \n", "\n", "AI, in simple terms, is the ability of computers to mimic human intelligence – learning, problem-solving, and making decisions. It's already woven into the fabric of our daily lives, often working behind the scenes to make things easier, faster, and more personalized. \n", "\n", "Think about your smartphone. It uses AI to personalize your recommendations, understand your voice commands, and even predict the words you're about to type. Streaming services leverage AI to curate your watchlist, while navigation apps use it to calculate the most efficient routes.  Even healthcare is being transformed by AI, with algorithms assisting doctors in diagnosing diseases and accelerating the discovery of new drugs.\n", "\n", "But AI's impact is just beginning. As technology advances, we can expect even more transformative applications to emerge, shaping the way we learn, work, live, and create. \n", "\n", "**AI-Powered Education:** \n", "\n", "Get ready for personalized learning experiences tailored to your individual needs and pace. AI-powered tutors can provide individualized support, while automated grading systems free up teachers to focus on more creative and engaging activities. Imagine virtual mentors guiding you through complex concepts or providing insightful feedback on your writing. \n", "\n", "**Healthcare Revolutionized:**\n", "\n", "AI has the potential to revolutionize healthcare, enabling earlier disease detection and prevention. Personalized medicine, tailored to your unique genetic makeup and lifestyle, will become the norm.  Even surgical procedures could be revolutionized with the help of robot-assisted surgery, leading to greater precision and faster recovery times.\n", "\n", "**Smart Homes and Cities:**\n", "\n", "Our homes and cities will become increasingly intelligent, with AI-powered systems managing everything from lighting and temperature to security and traffic flow. Imagine a home that anticipates your needs, adjusting the thermostat before you even feel the chill, or a city that optimizes traffic patterns, reducing congestion and emissions.\n", "\n", "**AI and Creativity:**\n", "\n", "Prepare for a new era of creativity, where AI becomes a powerful tool for artistic expression. AI-assisted writing tools can help you overcome writer's block, while AI algorithms can generate stunning artwork and compose original music.  \n", "\n", "**Navigating the Challenges:**\n", "\n", "While the future of AI is brimming with possibilities, we must also address the ethical challenges it presents.  \n", "\n", "* **Job displacement:**  AI-powered automation might lead to job losses in certain sectors, requiring us to adapt our workforce and explore new career paths.  \n", "\n", "* **Data privacy and security:** Protecting personal data used by AI systems is crucial to prevent misuse and ensure individual privacy. \n", "\n", "* **Bias and fairness:** AI algorithms can perpetuate societal biases if they are trained on biased data.  Addressing this issue is essential to ensure fairness and equity.\n", "\n", "* **Transparency and accountability:**  We need to understand how AI systems make decisions and who is responsible for the outcomes.  Explainable AI and clear accountability frameworks are essential for building trust in AI.\n", "\n", "**Embracing the Future:**\n", "\n", "The future of AI is undeniably exciting, filled with potential to improve our lives in countless ways. As we navigate this uncharted territory, it's essential to engage in thoughtful discussions about the ethical implications of AI, ensuring that it benefits all of humanity. By embracing the possibilities while addressing the challenges responsibly, we can shape a future where AI empowers us to create a better world. \n", "\n", "\n", "\n"]}], "source": ["print(response['content'].content)"]}, {"cell_type": "code", "execution_count": 27, "id": "3ea50a52", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The blog post is well-written and engaging, offering a clear overview of AI's current applications and future potential. Here's a breakdown and score:\n", "\n", "**Strengths:**\n", "\n", "* **Compelling Introduction:** The hook effectively grabs the reader's attention with a glimpse into a future shaped by AI.\n", "* **Clear and Accessible Language:** The post avoids technical jargon, making AI concepts understandable to a broad audience.\n", "* **Well-Organized Structure:**  The outline is followed effectively, with clear headings and transitions.\n", "* **Strong Examples:**  The use of real-world examples (smartphones, streaming services, healthcare) helps illustrate AI's impact.\n", "* **Forward-Looking Perspective:** The post explores exciting possibilities in education, healthcare, smart homes, and creativity.\n", "* **Balanced Discussion of Challenges:** The post acknowledges ethical concerns like job displacement, data privacy, bias, and transparency.\n", "* **Call to Action:** The conclusion encourages readers to engage in thoughtful discussions about AI's implications.\n", "\n", "**Areas for Improvement:**\n", "\n", "* **Deeper Dive into Challenges:** While the ethical challenges are mentioned, they could be explored in more depth. Expanding on potential solutions or mitigation strategies would strengthen this section.\n", "* **Specificity in Future Possibilities:** Some sections, like \"AI-Powered Education,\" could benefit from more specific examples of how AI will transform these fields.\n", "\n", "**Overall Score:** 8/10\n", "\n", "The blog post is well-written, informative, and thought-provoking. It provides a good overview of AI's potential to shape our lives while highlighting the importance of addressing ethical considerations. \n", "\n", "\n", "\n"]}], "source": ["print(response['score'].content)"]}, {"cell_type": "code", "execution_count": null, "id": "bd1cae43", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lang", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}