from typing import Annotated
from typing_extensions import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages 
from langchain_groq import ChatGroq
import dotenv

groq_api_key = dotenv.get_key(".env", "GROQ_API_KEY")

import os
os.environ["GROQ_API_KEY"] = groq_api_key
llm = ChatGroq( model="gemma2-9b-it")
llm

class blog_state(TypedDict):
    title: str
    outline: str
    content: str
    score: int

def create_outline(state: blog_state) -> blog_state:
    title = state['title']
    prompt = f"Create an outline for a blog post titled '{title}'."
    response = llm.invoke(prompt)
    state['outline'] = response
    return state

def create_content(state: blog_state) -> blog_state:
    outline = state['outline']
    prompt = f"Create a detailed blog post based on the outline: {outline}."
    response = llm.invoke(prompt)
    state['content'] = response
    return state

def evaluate_content(state: blog_state) -> blog_state:
    outline = state['outline']
    content = state['content']
    prompt = f"On the base of the outline: {outline} Evaluate the quality of the following blog post, give score from 0 to 10: content: {content}."
    response = llm.invoke(prompt)
    state['score'] = response
    return state

graph = StateGraph(blog_state)

graph.add_node('outline', create_outline)
graph.add_node('content', create_content)
graph.add_node('evaluate', evaluate_content)

graph.add_edge(START,'outline')
graph.add_edge('outline', 'content')
graph.add_edge('content', 'evaluate')
graph.add_edge('evaluate', END)

workflow = graph.compile()
workflow

response = workflow.invoke({'title': 'The Future of AI in Everyday Life'})

print(response['outline'].content)

print(response['content'].content)

print(response['score'].content)

