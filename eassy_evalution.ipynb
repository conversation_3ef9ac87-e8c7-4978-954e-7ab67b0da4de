from typing import Annotated
from typing import TypedDict, Annotated
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages 
from langchain_groq import ChatGroq
import dotenv
import os
from pydantic import BaseModel, Field

essay = """
Artificial Intelligence (AI) has rapidly transformed from a futuristic idea into a major force shaping today’s world. AI refers to the simulation of human intelligence in machines that are programmed to think, learn, and problem-solve. From smartphones and online recommendations to self-driving cars and medical diagnosis, AI is deeply embedded in modern life. Its increasing use has brought many benefits, but it also presents serious challenges and risks.

One of the most significant advantages of AI is its ability to perform tasks efficiently and accurately. In industries such as manufacturing and logistics, AI-powered robots and systems have streamlined production processes, reduced errors, and improved safety. In healthcare, AI is being used to detect diseases early, assist in surgeries, and manage patient data. AI also plays a key role in education by offering personalized learning experiences, adaptive testing, and automated grading systems.

Moreover, AI has revolutionized the way businesses operate. It enables data analysis at massive scale, helping companies make better decisions and predict market trends. Virtual assistants like <PERSON><PERSON>, <PERSON><PERSON>, and Google Assistant use AI to respond to user commands, manage schedules, and provide instant information. In agriculture, AI is improving crop management by analyzing weather, soil, and growth patterns. Even in everyday life, AI is present in spam filters, facial recognition, and translation tools.

However, the rise of AI is not without its negative effects. One of the biggest concerns is job displacement. As machines take over tasks that were once performed by humans, many workers fear unemployment, especially in sectors like manufacturing, customer service, and transportation. This creates a growing need for reskilling and adapting the workforce to new roles that complement AI.

Another issue is bias and discrimination. Since AI systems learn from data, they can inherit human biases present in that data. This can lead to unfair treatment in areas like hiring, lending, or policing. For example, facial recognition software has shown higher error rates for people of color, raising ethical and privacy concerns.

Privacy is another major challenge. AI systems collect and analyze vast amounts of personal data, sometimes without individuals fully understanding how their information is being used. This raises concerns about surveillance, data misuse, and a loss of personal freedom.

There’s also the risk of AI misuse, including deepfakes, autonomous weapons, and cyberattacks. These technologies can be exploited by malicious actors to spread misinformation, manipulate public opinion, or cause harm on a large scale.

In conclusion, AI is a powerful tool that has the potential to greatly improve the quality of life and advance many sectors of society. However, it must be used responsibly. Governments, industries, and individuals must work together to set ethical guidelines, ensure fairness, and minimize the negative impacts. With thoughtful planning and regulation, AI can continue to benefit humanity without compromising values like privacy, security, and equity.
"""

groq_api_key = dotenv.get_key(".env", "GROQ_API_KEY")

os.environ["GROQ_API_KEY"] = groq_api_key
llm = ChatGroq( model="gemma2-9b-it")
llm

class evaluation_schema(BaseModel):
    feedback : str = Field(description="Feedback on the essay")
    score : list[int] = Field(description="give me a Score for the essay out of 10 in integer", ge=0, le=10)

structured_model = llm.with_structured_output(evaluation_schema)

class eassy(TypedDict):
    essay : str
    lang_feedback : str
    analysis_feedback : str
    clarity_feedback : str
    overall_feedback : str
    individual_score : Annotated[list[int],add_messages]
    avg_score : float

def evaluate_lang(state: eassy):
    prompt = f"""Evaluate the following essay on the basis of language used, give feedback and score it in the range of 0 to 10:
    {eassy['essay']}
    """
    output = structured_model.invoke(prompt)
    return {'lang_feedback': output.feedback, 'individual_score': output.score}

def evaluate_analysis(state: eassy):
    prompt = f"""Evaluate the following essay on the basis of deapth of analysis used, give feedback and score it in the range of 0 to 10:
    {eassy['essay']}
    """
    output = structured_model.invoke(prompt)
    return {'analysis_feedback': output.feedback, 'individual_score': output.score}

def evaluate_clarity(state: eassy):
    prompt = f"""Evaluate the following essay on the basis of clarity this essay presents, give feedback and score it in the range of 0 to 10:
    {eassy['essay']}
    """
    output = structured_model.invoke(prompt)
    return {'clarity_feedback': output.feedback, 'individual_score': output.score}

def final_evaluation(state: eassy):
    prompt = f"""based on the following feedbacks, language: {eassy['lang_feedback']}, analysis: {eassy['analysis_feedback']}, clarity: {eassy['clarity_feedback']}, give an overall feedback and score it in the range of 0 to 10"""
    overall_output = llm.invoke(prompt).content

    ## avg of scores
    avg_score = sum(eassy['individual_score'])/len(eassy['individual_score'])
    return {'overall_feedback': overall_output, 'avg_score': avg_score}

graph = StateGraph(eassy)

graph.add_node('evaluate_lang', evaluate_lang)
graph.add_node('evaluate_analysis', evaluate_analysis)
graph.add_node('evaluate_clarity', evaluate_clarity)
graph.add_node('final_evaluation', final_evaluation)

graph.add_edge(START, 'evaluate_lang')
graph.add_edge(START, 'evaluate_analysis')
graph.add_edge(START, 'evaluate_clarity')

graph.add_edge('evaluate_lang', 'final_evaluation')
graph.add_edge('evaluate_analysis', 'final_evaluation')
graph.add_edge('evaluate_clarity', 'final_evaluation')

graph.add_edge('final_evaluation', END)

workflow = graph.compile()
workflow

response = workflow.invoke({'essay': essay})

print(response)