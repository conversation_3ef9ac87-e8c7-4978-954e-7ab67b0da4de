{"cells": [{"cell_type": "code", "execution_count": 126, "id": "e2fa2411", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from typing import TypedDict, Annotated\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages \n", "from langchain_groq import ChatGroq\n", "import dotenv\n", "import os\n", "from pydantic import BaseModel, Field\n", "import operator"]}, {"cell_type": "code", "execution_count": 127, "id": "2258244c", "metadata": {}, "outputs": [], "source": ["essay = \"\"\"\n", "Artificial Intelligence (AI) has rapidly transformed from a futuristic idea into a major force shaping today’s world. AI refers to the simulation of human intelligence in machines that are programmed to think, learn, and problem-solve. From smartphones and online recommendations to self-driving cars and medical diagnosis, AI is deeply embedded in modern life. Its increasing use has brought many benefits, but it also presents serious challenges and risks.\n", "\n", "One of the most significant advantages of AI is its ability to perform tasks efficiently and accurately. In industries such as manufacturing and logistics, AI-powered robots and systems have streamlined production processes, reduced errors, and improved safety. In healthcare, AI is being used to detect diseases early, assist in surgeries, and manage patient data. AI also plays a key role in education by offering personalized learning experiences, adaptive testing, and automated grading systems.\n", "\n", "Moreover, AI has revolutionized the way businesses operate. It enables data analysis at massive scale, helping companies make better decisions and predict market trends. Virtual assistants like <PERSON><PERSON>, <PERSON><PERSON>, and Google Assistant use AI to respond to user commands, manage schedules, and provide instant information. In agriculture, AI is improving crop management by analyzing weather, soil, and growth patterns. Even in everyday life, AI is present in spam filters, facial recognition, and translation tools.\n", "\n", "However, the rise of AI is not without its negative effects. One of the biggest concerns is job displacement. As machines take over tasks that were once performed by humans, many workers fear unemployment, especially in sectors like manufacturing, customer service, and transportation. This creates a growing need for reskilling and adapting the workforce to new roles that complement AI.\n", "\n", "Another issue is bias and discrimination. Since AI systems learn from data, they can inherit human biases present in that data. This can lead to unfair treatment in areas like hiring, lending, or policing. For example, facial recognition software has shown higher error rates for people of color, raising ethical and privacy concerns.\n", "\n", "Privacy is another major challenge. AI systems collect and analyze vast amounts of personal data, sometimes without individuals fully understanding how their information is being used. This raises concerns about surveillance, data misuse, and a loss of personal freedom.\n", "\n", "There’s also the risk of AI misuse, including deepfakes, autonomous weapons, and cyberattacks. These technologies can be exploited by malicious actors to spread misinformation, manipulate public opinion, or cause harm on a large scale.\n", "\n", "In conclusion, AI is a powerful tool that has the potential to greatly improve the quality of life and advance many sectors of society. However, it must be used responsibly. Governments, industries, and individuals must work together to set ethical guidelines, ensure fairness, and minimize the negative impacts. With thoughtful planning and regulation, AI can continue to benefit humanity without compromising values like privacy, security, and equity.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 128, "id": "93b4817e", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGroq(client=<groq.resources.chat.completions.Completions object at 0x000001A64A2BE950>, async_client=<groq.resources.chat.completions.AsyncCompletions object at 0x000001A649111450>, model_name='gemma2-9b-it', model_kwargs={}, groq_api_key=SecretStr('**********'))"]}, "execution_count": 128, "metadata": {}, "output_type": "execute_result"}], "source": ["groq_api_key = dotenv.get_key(\".env\", \"GROQ_API_KEY\")\n", "\n", "os.environ[\"GROQ_API_KEY\"] = groq_api_key\n", "llm = ChatGroq( model=\"gemma2-9b-it\")\n", "llm"]}, {"cell_type": "code", "execution_count": 129, "id": "81beb13d", "metadata": {}, "outputs": [], "source": ["class evaluation_schema(BaseModel):\n", "    feedback : str = Field(description=\"Fe<PERSON>back on the essay\")\n", "    score : int = Field(description=\"give me a Score for the essay out of 10 in integer\", ge=0, le=10)"]}, {"cell_type": "code", "execution_count": 130, "id": "5404ae76", "metadata": {}, "outputs": [], "source": ["structured_model = llm.with_structured_output(evaluation_schema)"]}, {"cell_type": "code", "execution_count": 131, "id": "a1bd203d", "metadata": {}, "outputs": [], "source": ["class eassy(TypedDict):\n", "    essay : str\n", "    lang_feedback : str\n", "    analysis_feedback : str\n", "    clarity_feedback : str\n", "    overall_feedback : str\n", "    individual_score : Annotated[list[int],operator.add]\n", "    avg_score : float"]}, {"cell_type": "code", "execution_count": 132, "id": "7e397f98", "metadata": {}, "outputs": [], "source": ["def evaluate_lang(state: eassy):\n", "    prompt = f\"\"\"Evaluate the following essay on the basis of language used, give feedback and score it in the range of 0 to 10:\n", "    {state['essay']}\n", "    \"\"\"\n", "    output = structured_model.invoke(prompt)\n", "    return {'lang_feedback': output.feedback, 'individual_score': [output.score]}"]}, {"cell_type": "code", "execution_count": 133, "id": "3a53d107", "metadata": {}, "outputs": [], "source": ["def evaluate_analysis(state: eassy):\n", "    prompt = f\"\"\"Evaluate the following essay on the basis of deapth of analysis used, give feedback and score it in the range of 0 to 10:\n", "    {state['essay']}\n", "    \"\"\"\n", "    output = structured_model.invoke(prompt)\n", "    return {'analysis_feedback': output.feedback, 'individual_score': [output.score]}"]}, {"cell_type": "code", "execution_count": 134, "id": "010e52c0", "metadata": {}, "outputs": [], "source": ["def evaluate_clarity(state: eassy):\n", "    prompt = f\"\"\"Evaluate the following essay on the basis of clarity this essay presents, give feedback and score it in the range of 0 to 10:\n", "    {state['essay']}\n", "    \"\"\"\n", "    output = structured_model.invoke(prompt)\n", "    return {'clarity_feedback': output.feedback, 'individual_score': [output.score]}"]}, {"cell_type": "code", "execution_count": 135, "id": "f14e176a", "metadata": {}, "outputs": [], "source": ["def final_evaluation(state: eassy):\n", "    prompt = f\"\"\"based on the following feedbacks, language: {state['lang_feedback']}, analysis: {state['analysis_feedback']}, clarity: {state['clarity_feedback']}, give an overall feedback and score it in the range of 0 to 10\"\"\"\n", "    overall_output = llm.invoke(prompt).content\n", "\n", "    ## avg of scores\n", "    avg_score = sum(state['individual_score'])/len(state['individual_score'])\n", "    return {'overall_feedback': overall_output, 'avg_score': avg_score}"]}, {"cell_type": "code", "execution_count": 136, "id": "377bf490", "metadata": {}, "outputs": [{"data": {"text/plain": ["<langgraph.graph.state.StateGraph at 0x1a649110f50>"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["graph = StateGraph(eassy)\n", "\n", "graph.add_node('evaluate_lang', evaluate_lang)\n", "graph.add_node('evaluate_analysis', evaluate_analysis)\n", "graph.add_node('evaluate_clarity', evaluate_clarity)\n", "graph.add_node('final_evaluation', final_evaluation)"]}, {"cell_type": "code", "execution_count": 137, "id": "bdedaf93", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<langgraph.graph.state.CompiledStateGraph object at 0x000001A6490DF130>"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.add_edge(START, 'evaluate_lang')\n", "graph.add_edge(START, 'evaluate_analysis')\n", "graph.add_edge(START, 'evaluate_clarity')\n", "\n", "graph.add_edge('evaluate_lang', 'final_evaluation')\n", "graph.add_edge('evaluate_analysis', 'final_evaluation')\n", "graph.add_edge('evaluate_clarity', 'final_evaluation')\n", "\n", "graph.add_edge('final_evaluation', END)\n", "\n", "workflow = graph.compile()\n", "workflow"]}, {"cell_type": "code", "execution_count": 138, "id": "7c046a0f", "metadata": {}, "outputs": [], "source": ["response = workflow.invoke({'essay': essay})"]}, {"cell_type": "code", "execution_count": 139, "id": "01276d35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[7, 8, 8]\n"]}], "source": ["print(response['individual_score'])"]}, {"cell_type": "code", "execution_count": 141, "id": "f484c15e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.666666666666667\n"]}], "source": ["print(response['avg_score' ])"]}], "metadata": {"kernelspec": {"display_name": "lang", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}